package com.ybmmarket20.utils

import android.content.Context
import android.content.SharedPreferences

/**
 * 搜索引导管理器
 * 用于管理搜索页面各种引导的显示状态
 */
object SearchGuideManager {

    // SharedPreferences文件名，用于存储搜索引导相关的状态数据
    private const val PREF_NAME = "search_guide_prefs"

    // 配送时效引导首次显示时间的存储key前缀，后面会拼接用户标识
    // 完整key格式：delivery_guide_first_shown_time_{merchantId}
    private const val KEY_DELIVERY_GUIDE_FIRST_SHOWN_TIME = "delivery_guide_first_shown_time_"

    // 配送时效引导显示次数的存储key前缀，后面会拼接用户标识
    // 完整key格式：delivery_guide_show_count_{merchantId}
    // 值含义：0=从未显示，1=显示过1次，2=显示过2次（不再显示）
    private const val KEY_DELIVERY_GUIDE_SHOW_COUNT = "delivery_guide_show_count_"
    // 12小时的毫秒数：12小时 × 60分钟 × 60秒 × 1000毫秒 = 43,200,000毫秒
    private const val TWELVE_HOURS_IN_MILLIS = 12 * 60 * 60 * 1000L

    private fun getPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }

    /**
     * 获取当前用户的唯一标识
     * 用于区分不同用户账号的引导显示状态，确保每个账号独立管理
     *
     * @return 用户唯一标识，优先使用merchantId，如果为空则使用默认值
     */
    private fun getCurrentUserKey(): String {
        val merchantId = SpUtil.getMerchantid()
        return if (merchantId.isNotEmpty()) merchantId else "default_user"
    }

    /**
     * 检查配送时效引导是否已经显示过（兼容旧版本）
     */
    @Deprecated("使用新的时间控制逻辑")
    fun isDeliveryGuideShown(context: Context): Boolean {
        return getPreferences(context).getBoolean("delivery_guide_shown", false)
    }

    /**
     * 标记配送时效引导已显示
     * 根据当前显示次数进行不同的处理：
     * - 第一次显示：记录显示时间戳和显示次数为1
     * - 第二次显示：只增加显示次数为2，不再记录时间
     * - 第三次及以后：不做任何处理（实际上不会调用到，因为shouldShowDeliveryGuide会返回false）
     *
     * @param context 上下文，用于获取SharedPreferences
     */
    fun markDeliveryGuideShown(context: Context) {
        val userKey = getCurrentUserKey()
        val prefs = getPreferences(context)
        val currentTime = System.currentTimeMillis()

        // 获取当前显示次数
        val showCount = prefs.getInt(KEY_DELIVERY_GUIDE_SHOW_COUNT + userKey, 0)

        if (showCount == 0) {
            // 第一次显示，记录时间戳和显示次数
            prefs.edit()
                .putLong(KEY_DELIVERY_GUIDE_FIRST_SHOWN_TIME + userKey, currentTime)
                .putInt(KEY_DELIVERY_GUIDE_SHOW_COUNT + userKey, 1)
                .apply()
        } else if (showCount == 1) {
            // 第二次显示，只增加显示次数，不再记录时间
            prefs.edit()
                .putInt(KEY_DELIVERY_GUIDE_SHOW_COUNT + userKey, 2)
                .apply()
        }
        // showCount >= 2 的情况不处理，因为不应该再显示了
    }

    /**
     * 重置配送时效引导状态（用于测试）
     * 清除当前用户的所有引导相关数据，包括：
     * - 首次显示时间戳
     * - 显示次数计数
     * - 旧版本的显示标记（兼容性清理）
     *
     * 调用此方法后，引导将重新按照初次显示的逻辑执行
     *
     * @param context 上下文，用于获取SharedPreferences
     */
    fun resetDeliveryGuideState(context: Context) {
        val userKey = getCurrentUserKey()
        getPreferences(context).edit()
            .remove(KEY_DELIVERY_GUIDE_FIRST_SHOWN_TIME + userKey)  // 清除首次显示时间
            .remove(KEY_DELIVERY_GUIDE_SHOW_COUNT + userKey)        // 清除显示次数
            .remove("delivery_guide_shown")                         // 清除旧版本的标记（兼容性）
            .apply()
    }

    /**
     * 检查是否应该显示配送时效引导
     * 显示规则：
     * 1. 第一次进入有配送时效时显示
     * 2. 时隔12小时后再次进入有配送时效时还会显示
     * 3. 第二次显示后不再显示
     *
     * @param context 上下文
     * @param hasDeliveryOptions 搜索结果是否包含配送时效选项
     * @return 是否应该显示引导
     */
    fun shouldShowDeliveryGuide(context: Context, hasDeliveryOptions: Boolean): Boolean {
        if (!hasDeliveryOptions) {
            return false
        }

        val userKey = getCurrentUserKey()
        val prefs = getPreferences(context)
        val showCount = prefs.getInt(KEY_DELIVERY_GUIDE_SHOW_COUNT + userKey, 0)

        when (showCount) {
            0 -> {
                // 情况1：从未显示过，应该显示
                // 这是用户第一次进入有配送时效的搜索页面
                return true
            }
            1 -> {
                // 情况2：显示过一次，检查是否已过12小时
                // 获取第一次显示的时间戳
                val firstShownTime = prefs.getLong(KEY_DELIVERY_GUIDE_FIRST_SHOWN_TIME + userKey, 0)
                val currentTime = System.currentTimeMillis()

                // 计算时间差，如果超过12小时则允许第二次显示
                return (currentTime - firstShownTime) >= TWELVE_HOURS_IN_MILLIS
            }
            else -> {
                // 情况3：已显示过2次或更多，永久不再显示
                // showCount >= 2 的情况，用户已经看过两次引导了
                return false
            }
        }
    }
}
