package com.ybmmarket20.utils

import android.content.Context
import android.content.SharedPreferences

/**
 * 搜索引导管理器
 * 用于管理搜索页面各种引导的显示状态
 */
object SearchGuideManager {

    private const val PREF_NAME = "search_guide_prefs"
    private const val KEY_DELIVERY_GUIDE_FIRST_SHOWN_TIME = "delivery_guide_first_shown_time_"
    private const val KEY_DELIVERY_GUIDE_SHOW_COUNT = "delivery_guide_show_count_"
    private const val TWELVE_HOURS_IN_MILLIS = 12 * 60 * 60 * 1000L // 12小时的毫秒数

    private fun getPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }

    /**
     * 获取当前用户的唯一标识
     */
    private fun getCurrentUserKey(): String {
        val merchantId = SpUtil.getMerchantid()
        return if (merchantId.isNotEmpty()) merchantId else "default_user"
    }

    /**
     * 检查配送时效引导是否已经显示过（兼容旧版本）
     */
    @Deprecated("使用新的时间控制逻辑")
    fun isDeliveryGuideShown(context: Context): Boolean {
        return getPreferences(context).getBoolean("delivery_guide_shown", false)
    }

    /**
     * 标记配送时效引导已显示
     */
    fun markDeliveryGuideShown(context: Context) {
        val userKey = getCurrentUserKey()
        val prefs = getPreferences(context)
        val currentTime = System.currentTimeMillis()

        // 获取当前显示次数
        val showCount = prefs.getInt(KEY_DELIVERY_GUIDE_SHOW_COUNT + userKey, 0)

        if (showCount == 0) {
            // 第一次显示，记录时间
            prefs.edit()
                .putLong(KEY_DELIVERY_GUIDE_FIRST_SHOWN_TIME + userKey, currentTime)
                .putInt(KEY_DELIVERY_GUIDE_SHOW_COUNT + userKey, 1)
                .apply()
        } else if (showCount == 1) {
            // 第二次显示，增加计数
            prefs.edit()
                .putInt(KEY_DELIVERY_GUIDE_SHOW_COUNT + userKey, 2)
                .apply()
        }
    }

    /**
     * 重置配送时效引导状态（用于测试）
     */
    fun resetDeliveryGuideState(context: Context) {
        val userKey = getCurrentUserKey()
        getPreferences(context).edit()
            .remove(KEY_DELIVERY_GUIDE_FIRST_SHOWN_TIME + userKey)
            .remove(KEY_DELIVERY_GUIDE_SHOW_COUNT + userKey)
            .remove("delivery_guide_shown") // 清除旧版本的标记
            .apply()
    }

    /**
     * 检查是否应该显示配送时效引导
     * 显示规则：
     * 1. 第一次进入有配送时效时显示
     * 2. 时隔12小时后再次进入有配送时效时还会显示
     * 3. 第二次显示后不再显示
     *
     * @param context 上下文
     * @param hasDeliveryOptions 搜索结果是否包含配送时效选项
     * @return 是否应该显示引导
     */
    fun shouldShowDeliveryGuide(context: Context, hasDeliveryOptions: Boolean): Boolean {
        if (!hasDeliveryOptions) {
            return false
        }

        val userKey = getCurrentUserKey()
        val prefs = getPreferences(context)
        val showCount = prefs.getInt(KEY_DELIVERY_GUIDE_SHOW_COUNT + userKey, 0)

        when (showCount) {
            0 -> {
                // 从未显示过，应该显示
                return true
            }
            1 -> {
                // 显示过一次，检查是否已过12小时
                val firstShownTime = prefs.getLong(KEY_DELIVERY_GUIDE_FIRST_SHOWN_TIME + userKey, 0)
                val currentTime = System.currentTimeMillis()
                return (currentTime - firstShownTime) >= TWELVE_HOURS_IN_MILLIS
            }
            else -> {
                // 已显示过2次或更多，不再显示
                return false
            }
        }
    }
}
