# 搜索配送时效引导组件

## 概述
这是一个用于搜索页面的配送时效引导组件，当用户首次进入搜索结果页面且包含配送时效筛选选项时，会显示引导蒙层来指导用户使用配送时效功能。

## 组件结构

### 1. SearchDeliveryGuideView.kt
主要的引导组件类，负责：
- 显示全屏半透明蒙层背景（覆盖整个屏幕包括状态栏）
- 手势指引图片：102dp×124dp，顶部与SearchDynamicLabelView或brand_rg_02顶部对齐，水平屏幕居中
- "我知道了"按钮：左右各16dp边距，高度自适应图片内容，顶部与手势图片底部对齐
- 处理点击事件：
  - 点击"我知道了"图片可关闭引导
  - 点击蒙版背景也可以关闭引导（可选）
- 支持两种定位模式：SearchDynamicLabelView优先，RadioGroup备选

### 2. SearchGuideManager.kt
引导状态管理工具类，负责：
- 管理引导显示状态（按用户账号分别管理）
- 实现12小时间隔的显示控制逻辑
- 使用SharedPreferences持久化状态和时间戳
- 提供重置功能用于测试

### 3. view_search_delivery_guide.xml
引导组件的布局文件，包含：
- 全屏半透明黑色背景（可点击关闭）
- 手势指引图片（固定尺寸，通过代码动态定位）
- "我知道了"按钮图片（自适应高度，通过代码动态定位）

## 使用方法

### 1. 在BaseSearchProductActivity中的集成
组件已经集成到BaseSearchProductActivity中：

```java
// 在initData方法中初始化
initDeliveryGuide();

// 在updateSearchData方法中检查显示
if (isFirst) {
    checkAndShowDeliveryGuide();
}
```

**关键实现细节**：
- 使用`getWindow().getDecorView()`作为父容器，确保全屏覆盖
- 通过`getLocationInWindow()`获取目标组件的屏幕绝对位置
- 动态计算并设置组件的精确位置和尺寸

### 2. 显示条件
引导会在以下条件都满足时显示：
- 搜索结果页面首次加载（isFirst = true）
- 包含配送时效筛选选项（rbExpress按钮可见）
- **新增条件**：SearchDynamicLabelView中包含"配送时效"标签（优先级1）或mBrandRg02可见（优先级2）
- **显示规则**：
  - 每个账号第一次进入有配送时效时显示
  - 时隔12小时后再次进入有配送时效时还会显示
  - 第二次显示后不再显示

### 3. 图片资源
需要准备以下图片资源：
- `mask_hands.png`: 手势指引图片
- `mask_iknow.png`: "我知道了"按钮图片

### 4. 自定义使用
如果需要在其他页面使用，可以参考以下代码：

**Kotlin:**
```kotlin
// 创建引导组件
val deliveryGuideView = SearchDeliveryGuideView(context)
rootLayout.addView(deliveryGuideView)

// 设置点击监听
deliveryGuideView.setOnGuideClickListener(object : SearchDeliveryGuideView.OnGuideClickListener {
    override fun onGuideClick() {
        SearchGuideManager.markDeliveryGuideShown(context)
        // 其他逻辑
    }
})

// 显示引导
if (SearchGuideManager.shouldShowDeliveryGuide(context, hasDeliveryOptions)) {
    targetView.post {
        deliveryGuideView.showGuide(targetRadioGroup)
    }
}
```

**Java:**
```java
// 创建引导组件
SearchDeliveryGuideView deliveryGuideView = new SearchDeliveryGuideView(context);
rootLayout.addView(deliveryGuideView);

// 设置点击监听
deliveryGuideView.setOnGuideClickListener(new SearchDeliveryGuideView.OnGuideClickListener() {
    @Override
    public void onGuideClick() {
        SearchGuideManager.INSTANCE.markDeliveryGuideShown(context);
        // 其他逻辑
    }
});

// 显示引导
if (SearchGuideManager.INSTANCE.shouldShowDeliveryGuide(context, hasDeliveryOptions)) {
    targetView.post(() -> {
        deliveryGuideView.showGuide(targetRadioGroup);
    });
}
```

## API说明

### SearchDeliveryGuideView

#### 方法
- `showGuide(targetDynamicLabelView: SearchDynamicLabelView?)`: 显示引导，相对于SearchDynamicLabelView定位
- `showGuide(targetRadioGroup: RadioGroup?)`: 显示引导，相对于RadioGroup定位
- `hideGuide()`: 隐藏引导
- `setOnGuideClickListener(listener: OnGuideClickListener?)`: 设置点击监听器
- `isShowing(): Boolean`: 检查是否正在显示
- `hasDeliveryTimeLabel(dynamicLabelView: SearchDynamicLabelView?)`: 检查是否包含"配送时效"标签

### SearchGuideManager

#### 方法
- `shouldShowDeliveryGuide(context: Context, hasDeliveryOptions: Boolean): Boolean`: 检查是否应该显示引导（支持12小时间隔逻辑）
- `markDeliveryGuideShown(context: Context)`: 标记引导已显示（记录时间戳和显示次数）
- `resetDeliveryGuideState(context: Context)`: 重置引导状态（测试用）
- `getCurrentUserKey()`: 获取当前用户的唯一标识（基于merchantId）

## 注意事项

1. **全屏蒙层**: 组件覆盖整个屏幕包括状态栏
   - 点击"我知道了"图片可以关闭引导
   - 点击蒙版背景也可以关闭引导（可选）
2. **智能定位**:
   - 优先使用SearchDynamicLabelView作为定位参考（如果可见且包含"配送时效"标签）
   - 备选使用brand_rg_02作为定位参考
   - 手势图片：102dp×124dp，顶部与参考组件顶部对齐（带-20dp偏移修正），水平屏幕居中
   - "我知道了"按钮：左右各16dp边距，高度自适应图片内容，顶部与手势图片底部对齐
3. **显示控制**:
   - 按用户账号（merchantId）分别管理显示状态
   - 第一次显示后记录时间戳
   - 12小时后允许第二次显示
   - 第二次显示后永久不再显示
4. **生命周期**: 引导组件会在点击后自动隐藏并标记状态
5. **性能**: 使用post方法确保在布局完成后再显示引导
6. **测试**: 可以使用resetDeliveryGuideState方法重置状态进行测试

## 埋点
组件集成了埋点功能：
- 引导显示时的曝光埋点
- 用户点击引导时的点击埋点

## 扩展性
如果需要添加其他类型的引导，可以：
1. 在SearchGuideManager中添加新的状态管理
2. 创建新的引导组件类
3. 在相应的页面中集成使用
